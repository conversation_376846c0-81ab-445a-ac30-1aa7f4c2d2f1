import api from "./";

/**
 * Create a visit with guest.
 *
 * @param {object} visitData - The data for the new visit with guest.
 * @returns {Promise<any>} A promise that resolves to the created visit data.
 */
export const createVisitWithGuest = async (visitData) => {
  const response = await api.post("/visits/create-with-guest", visitData);
  return response.data;
};

/**
 * Check in a guest for a specific visit.
 *
 * @param {string} visitId - The ID of the visit.
 * @param {string} guestId - The ID of the guest.
 * @returns {Promise<any>} A promise that resolves to the check-in response.
 */
export const checkinGuestForVisit = async (visitId, guestId) => {
  const response = await api.post(`/visits/${visitId}/guest/${guestId}/checkin`);
  return response.data;
};


/**
 * Get guests for a specific visit in a facility.
 *
 * @param {string} facilityId - The ID of the facility.
 * @param {string} visitId - The ID of the visit.
 * @returns {Promise<any>} A promise that resolves to the list of guests.
 */
export const getVisitGuests = async (facilityId, visitId) => {
  const response = await api.get(`/visits/facility/${facilityId}/visit/${visitId}/guests`);
  return response.data;
};


/**
 * Check out a guest for a specific visit.
 *
 * @param {string} visitId - The ID of the visit.
 * @param {string} guestId - The ID of the guest.
 * @returns {Promise<any>} A promise that resolves to the checkout response.
 */
export const checkoutGuestForVisit = async (visitId, guestId) => {
  const response = await api.post(`/visits/${visitId}/guest/${guestId}/checkout`);
  return response.data;
};
