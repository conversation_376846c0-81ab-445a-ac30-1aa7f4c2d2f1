import React, { useState, useRef } from "react";
import { useNavigate } from 'react-router-dom';
import DateInput from "../../Components/Global/Input/ValidationHubDate";
import GenericTable from "../../Components/GenericTable";
import DetailsCard from "../../Components/Global/DetailsCard";
import PrintModal from "../../Components/Global/PrintModal";
import EditPhotoModal from "../../Components/Global/ImageAndCamera/EditPhotoModal";
import HostSearch from "./HostSearch";
import VisitorSearch from "./VisitorSearch";
import useClickOutside from "../InpatientVisit/useClickOutside";
import Button from "../../Components/Global/Button";
import VisitorForm from "../../Components/Global/Forms/VisitorForm";
import defaultImg from "../../Images/demoimg.svg"
import moment from "moment";
import { FilterButtons } from "../../Components/GenericTable";
import { VisitorsDoctorsData } from "../../api/static"
import { getVisitorColumns } from "../../api/tableDataColumns"; 
import homeicon from "../../Images/home-icon.svg";
import CreateVisitorModal from "../../Components/Global/CreateVisitorModal"; // Import the modal for creating a new visitor
import { searchIdentities } from "../../api/global"; // <-- import the API
import formatDateTime from "../../utils/formatDate";

const ReceptionDesk = () => {
  // ---------------- State Variables ----------------
  const [searchTerm, setSearchTerm] = useState("");
  const [hostSearchTerm, setHostSearchTerm] = useState("");
  const [tableSearchTerm, setTableSearchTerm] = useState("");
  const navigate = useNavigate();
  const [searchResults, setSearchResults] = useState([]);
  const [isDropdownVisible, setIsDropdownVisible] = useState(false);
  const [guestSearchTerm, setGuestSearchTerm] = useState("");
  const [guestSearchResults, setGuestSearchResults] = useState([]);
  const [isGuestDropdownVisible, setIsGuestDropdownVisible] = useState(false);
  const [selectedPatient, setSelectedPatient] = useState(null);

  // State for the complete list of visitors (across all hosts)
  const [allGuests, setAllGuests] = useState(() =>
    VisitorsDoctorsData.flatMap((host) =>
      (host.visitorList || []).map((visitor) => ({
        ...visitor,
        hostName: host.name,
        facility: host.site,
      }))
    )
  );

  // We'll derive our final list based on the selected date and filter button.
  const [dateFilteredGuests, setDateFilteredGuests] = useState(allGuests);
  const [activeFilter, setActiveFilter] = useState("all");
  const [printModalVisible, setPrintModalVisible] = useState(false);
  const [selectedGuest, setSelectedGuest] = useState(null);
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [selectedGuestId, setSelectedGuestId] = useState(null);
  const [showVisitorForm, setShowVisitorForm] = useState(false);
  const [patientSearchPlaceholder, setPatientSearchPlaceholder] = useState("Search By Host Name, EID");
  const [guestSearchPlaceholder, setGuestSearchPlaceholder] = useState("Search By Guest");

  // New state for custom date picker
  const [selectedDate, setSelectedDate] = useState(new Date());
  const [isCreateVisitorModalOpen, setIsCreateVisitorModalOpen] = useState(false); // State for the create visitor modal

  // Refs for clickOutside logic
  const patientSearchRef = useRef(null);
  const guestSearchRef = useRef(null);
  useClickOutside(guestSearchRef, () => setIsGuestDropdownVisible(false));
  useClickOutside(patientSearchRef, () => setIsDropdownVisible(false));

  // ---------------- Handlers ----------------
  const handleHome = () => {
    setSelectedPatient(null);
    setPatientSearchPlaceholder("Search By Host Name, EID");
    setGuestSearchPlaceholder("Search By Guest");

    // Restore all visitors in the table
    const allVisitors = VisitorsDoctorsData.flatMap((host) =>
      (host.visitorList || []).map((visitor) => ({
        ...visitor,
        hostName: host.name,
        facility: host.site,
      }))
    );
    setAllGuests(allVisitors);
    setDateFilteredGuests(allVisitors);
  };

  const handleHostSearchInputChange = async (value) => {
    setSearchTerm(value);
    if (value.trim()) {
      // Use the API to search for hosts
      try {
        const apiResults = await searchIdentities(value);
        setSearchResults(apiResults?.data?.data || []); // <-- fix here
        setIsDropdownVisible(true);
      } catch (error) {
        setSearchResults([]);
        setIsDropdownVisible(false);
      }
    } else {
      setSearchResults([]);
      setIsDropdownVisible(false);
    }
  };

  const handleHostClick = (host) => {
    setSelectedPatient(host);
    const updatedGuestList = (host.visitorList || []).map((guest) => ({
      ...guest,
      hostName: host.name,
      facility: host.site,
    }));
    setAllGuests(updatedGuestList);
    setDateFilteredGuests(updatedGuestList);
    setIsDropdownVisible(false);
    setSearchTerm("");
    setPatientSearchPlaceholder(host.name);
    setShowVisitorForm(false)
  };

  const handleVisitorSearchInputChange = async (value) => {
    setGuestSearchTerm(value);
    if (value.trim()) {
      const filtered = VisitorsDoctorsData.flatMap((host) =>
        (host.visitorList || [])
          .filter((visitor) =>
            visitor.visitorName.toLowerCase().includes(value.toLowerCase())
          )
          .map((visitor) => ({
            ...visitor,
            hostName: host.name,
            eid: host.eid,
          }))
      );
      setGuestSearchResults(filtered);
      setIsGuestDropdownVisible(true);
    } else {
      setGuestSearchResults([]);
      setIsGuestDropdownVisible(false);
    }
  };

  const handleGuestClick = (visitor) => {
    const host = VisitorsDoctorsData.find((h) => h.id === visitor.hostId);
    if (host) {
      setSelectedPatient(host);
      const newGuestList = (host.visitorList || []).map((g) => ({
        ...g,
        hostName: host.name,
        facility: host.site,
      }));
      setAllGuests(newGuestList);
      setDateFilteredGuests(newGuestList);
    }
    setIsGuestDropdownVisible(false);
    setGuestSearchResults([]);
    setGuestSearchTerm("");
    setGuestSearchPlaceholder(visitor.visitorName);
    setShowVisitorForm(false);
  };

  const handleCreateVisitorClick = () => {
    setIsCreateVisitorModalOpen(true); // Open the create visitor modal
  };

  const handleCreateVisitorSubmit = (newVisitor) => {
    // Add the new visitor to the list
    if (selectedPatient) {
      newVisitor.hostName = selectedPatient.name;
      newVisitor.facility = selectedPatient.site;
    }
    const updatedAllGuests = [newVisitor, ...allGuests];
    setAllGuests(updatedAllGuests);
    setDateFilteredGuests(updatedAllGuests);
    setIsCreateVisitorModalOpen(false); // Close the modal
  };

  const handleImageCaptured = (imageData) => {
    const updatedGuests = dateFilteredGuests.map((guest) =>
      guest.id === selectedGuestId ? { ...guest, image: imageData } : guest
    );
    setAllGuests(updatedGuests);
    setDateFilteredGuests(updatedGuests);
    setIsModalOpen(false);
  };

  const openModal = (title, guestId) => {
    setSelectedGuestId(guestId);
    setIsModalOpen(true);
  };

  const handlePrintClick = (guest) => {
    setSelectedGuest(guest);
    setPrintModalVisible(true);
  };

  const handleClosePrintModal = () => {
    setPrintModalVisible(false);
    setSelectedGuest(null);
  };

  const handleAddVisitor = (newVisitor) => {
    if (selectedPatient) {
      newVisitor.hostName = selectedPatient.name;
      newVisitor.facility = selectedPatient.site;
    }
    const updatedAllGuests = [newVisitor, ...allGuests];
    setAllGuests(updatedAllGuests);
    setDateFilteredGuests(updatedAllGuests);
    console.log("Visitor Added:", newVisitor);
  };

  const handleHistoryOpen = () => {
    console.log("History panel opened");
  };
  // console.log(selectedPatient);


  // ---------------- Date Filtering ----------------
  const filterVisitorsByDate = (date) => {
    const formattedSelectedDate = moment(date).format("M-D-YYYY");
    const filteredVisitors = allGuests.filter((visitor) => {
      const visitorDate = moment(visitor.startDate, "M-D-YYYY h:mm A").format("M-D-YYYY");
      return visitorDate === formattedSelectedDate;
    });
    setDateFilteredGuests(filteredVisitors);
    setShowVisitorForm(false)
  };
  const visitorColumns = getVisitorColumns({
    openModal,
    handlePrintClick,
    // profileImage
  });
  // ---------------- Derived Data ----------------
  // Apply the active filter on top of the date filtered guests.
  const displayedGuestList = dateFilteredGuests.filter((visitor) => {
    if (activeFilter === "all") return true;
    if (activeFilter === "recent") return visitor.recent;
    if (activeFilter === "invited") return visitor.invited;
    if (activeFilter === "checkedin") return visitor.checkedIn;
    if (activeFilter === "checkedout") return visitor.checkedOut;
    if (activeFilter === "checkInDenied") return visitor.checkInDenied;
    return true;
  });

  // Filter options for the FilterButtons component
  const filterOptions = [
    { value: "recent", label: "Recent visitors" },
    { value: "all", label: "All Visitors" },
    { value: "invited", label: "All Invited" },
    { value: "checkedin", label: "Checked In" },
    { value: "checkedout", label: "Checked Out" },
    { value: "checkInDenied", label: "Check-in-denied" }
  ];

  return (
    <div className="pl-24 mt-20 pr-8 h-full">
      {/* Title */}
      <div className="text-[24px] font-normal text-[#4F2683]">
        <h3>Reception Desk</h3>
      </div>

      {/* Top Search, Custom Date Picker, Filter Buttons & Home Icon Row */}
      <div>
        <div className="flex flex-col sm:flex-row sm:justify-center items-center  sm:gap-6 my-4 mb-8">

          <Button type="imgbtn" className="px-[10px] py-2" icon={homeicon} onClick={handleHome} />

          {/* Host Search */}
          <HostSearch
            placeholder={patientSearchPlaceholder}
            searchTerm={searchTerm}
            onInputChange={(value) => {
              setHostSearchTerm(value);
              handleHostSearchInputChange(value); // your existing logic
            }}
            results={searchResults}
            onResultClick={handleHostClick}
            isDropdownVisible={isDropdownVisible}
            containerRef={patientSearchRef}
          />

          {/* Visitor Search */}
          <VisitorSearch
            placeholder={guestSearchPlaceholder}
            searchTerm={guestSearchTerm}
            onInputChange={handleVisitorSearchInputChange}
            results={guestSearchResults}
            onResultClick={handleGuestClick}
            isDropdownVisible={isGuestDropdownVisible}
            containerRef={guestSearchRef}
            onCreateClick={handleCreateVisitorClick} // Pass the create visitor handler
          />

          {/* Custom Date Picker */}
          <DateInput
            value={selectedDate}
            onChange={(date) => {
              setSelectedDate(date);
              filterVisitorsByDate(date);
            }}
            placeholder="Select a date"
            className="w-[25%]  rounded-md text-[#4F2683] "
          />

        </div>
      </div>
      {/* Render DetailsCard if a host is selected */}
      {selectedPatient && (
        <DetailsCard
          OpenPhotoModal={() => setIsModalOpen(true)}
          handleHistoryOpen={handleHistoryOpen}
          profileImage={defaultImg}
          defaultImage={defaultImg}
          name={selectedPatient?.name || "N/A"}
          showHistoryButton={false}
          additionalFields={[
            {
              label: "Job Tittle",
              value: selectedPatient?.jobTittle
            },
            {
              label: "EID",
              value: selectedPatient?.eid
            },
            {
              label: "Status",
              value: selectedPatient?.status
            },
            {
              label: "Start Date",
              value: selectedPatient?.startDate || "N/A",
            },
            {
              label: "End Date",
              value: selectedPatient?.endDate ? formatDateTime(selectedPatient.endDate) : "N/A",
            }
          ]}
        />
      )}
      {/* Render Visitor Form */}
      {showVisitorForm && (
        <VisitorForm
          onAddGuest={handleAddVisitor}
          onClose={() => setShowVisitorForm(false)}
          hostName={selectedPatient?.name} // Pass hostName to VisitorForm
        />
      )}
      <FilterButtons filter={activeFilter} onFilterChange={setActiveFilter} filterOptions={filterOptions} />

      {/* GenericTable for displaying visitors */}
      <div className="mt-4">
        <GenericTable
          title={"Guests"}
          searchTerm={tableSearchTerm}
          onSearchChange={(e) => setTableSearchTerm(e.target.value)}
          columns={visitorColumns}
          data={displayedGuestList}
          fixedHeader
          onAdd={() => setShowVisitorForm(true)}
          fixedHeaderScrollHeight="300px"
          highlightOnHover
          showAddButton={selectedPatient || selectedGuest ? true : false}
          striped={false}
        />
      </div>

      {/* Print Modal */}
      {printModalVisible && selectedGuest && (
        <PrintModal guest={selectedGuest} onClose={handleClosePrintModal} />
      )}

      {/* Edit Photo Modal */}
      {isModalOpen && (
        <EditPhotoModal onClose={() => setIsModalOpen(false)} onSave={handleImageCaptured} />
      )}

      {/* Create Visitor Modal */}
      {isCreateVisitorModalOpen && (
        <CreateVisitorModal
          isOpen={isCreateVisitorModalOpen}
          onClose={() => setIsCreateVisitorModalOpen(false)}
          onSubmit={handleCreateVisitorSubmit}
        />
      )}
    </div>
  );
};

export default ReceptionDesk;
